"""
Additional Ollama endpoint router for compatibility.

This module provides additional Ollama endpoints including running models
and model management stubs for API compatibility.
"""

import logging
from typing import Dict, Any

from fastapi import APIRouter, Depends, HTTPException, status

from app.api.schemas.ollama_schemas import (
    OllamaRunningModelsResponse,
    OllamaCopyRequest,
    OllamaDeleteRequest,
    OllamaPullRequest,
    OllamaPushRequest,
    OllamaCreateRequest,
)
from app.core.config import Settings, get_settings
from app.services.translation_service import TranslationService
from app.services.backend_client_service import (
    BackendClientService,
    BackendClientError,
    BackendTimeoutError,
    BackendConnectionError,
    BackendHTTPError,
    get_backend_client,
)

logger = logging.getLogger(__name__)

# Create router
router = APIRouter()


def get_translation_service(settings: Settings = Depends(get_settings)) -> TranslationService:
    """
    Dependency to get the translation service.

    Args:
        settings: Application settings

    Returns:
        TranslationService: Configured translation service
    """
    return TranslationService(settings)


async def get_backend_client_dependency(settings: Settings = Depends(get_settings)) -> BackendClientService:
    """
    Dependency to get the backend client service.

    Args:
        settings: Application settings

    Returns:
        BackendClientService: Configured backend client
    """
    return get_backend_client(settings)


@router.get(
    "/api/ps",
    response_model=OllamaRunningModelsResponse,
    summary="List Running Models",
    description="List models that are currently loaded into memory (simulated)",
    responses={
        200: {"description": "Successful running models list retrieval"},
        502: {"description": "Backend service error"},
        504: {"description": "Backend service timeout"},
    }
)
async def list_running_models(
    translation_service: TranslationService = Depends(get_translation_service),
    backend_client: BackendClientService = Depends(get_backend_client_dependency),
) -> OllamaRunningModelsResponse:
    """
    List running models in Ollama format (simulated).
    
    This endpoint simulates the Ollama /api/ps endpoint by retrieving the model list
    from the backend and presenting them as "running" models with mock runtime data.
    
    Args:
        translation_service: Service for format translation
        backend_client: Client for backend API communication
        
    Returns:
        OllamaRunningModelsResponse: The simulated running models list
        
    Raises:
        HTTPException: For various error conditions (502, 504)
    """
    logger.info("Running models list request received (simulated)")
    
    try:
        # Get model list from backend
        logger.debug("Requesting model list from backend for running models simulation")
        medusa_response = await backend_client.get_models()
        
        # Simulate running models
        logger.debug("Simulating running models from model list")
        ollama_response = translation_service.simulate_running_models(medusa_response)
        
        logger.info(f"Running models list retrieved successfully: {len(ollama_response.models)} models (simulated)")
        return ollama_response
        
    except BackendTimeoutError as e:
        logger.error(f"Backend timeout during running models request: {e}")
        raise HTTPException(
            status_code=504,
            detail="Backend service timeout while retrieving running models"
        ) from e
        
    except BackendConnectionError as e:
        logger.error(f"Backend connection error during running models request: {e}")
        raise HTTPException(
            status_code=502,
            detail="Backend service connection error"
        ) from e
        
    except BackendHTTPError as e:
        logger.error(f"Backend HTTP error during running models request: {e}")
        raise HTTPException(
            status_code=502,
            detail="Backend service error"
        ) from e
        
    except BackendClientError as e:
        logger.error(f"Backend client error during running models request: {e}")
        raise HTTPException(
            status_code=502,
            detail="Backend service error"
        ) from e
        
    except Exception as e:
        logger.error(f"Unexpected error during running models request: {e}")
        raise HTTPException(
            status_code=500,
            detail="Internal server error"
        ) from e


# Model management stub endpoints
@router.post(
    "/api/copy",
    summary="Copy Model (Not Supported)",
    description="Copy a model - not supported with current backend",
    responses={
        501: {"description": "Not implemented with current backend"},
    }
)
async def copy_model(request: OllamaCopyRequest) -> Dict[str, str]:
    """
    Copy model endpoint stub.
    
    This endpoint is not supported with the current OpenAI-compatible backend.
    """
    logger.warning(f"Copy model request not supported: {request.source} -> {request.destination}")
    raise HTTPException(
        status_code=501,
        detail="Model copying is not supported with the current backend. This feature requires Ollama-specific model management capabilities."
    )


@router.delete(
    "/api/delete",
    summary="Delete Model (Not Supported)",
    description="Delete a model - not supported with current backend",
    responses={
        501: {"description": "Not implemented with current backend"},
    }
)
async def delete_model(request: OllamaDeleteRequest) -> Dict[str, str]:
    """
    Delete model endpoint stub.
    
    This endpoint is not supported with the current OpenAI-compatible backend.
    """
    logger.warning(f"Delete model request not supported: {request.model}")
    raise HTTPException(
        status_code=501,
        detail="Model deletion is not supported with the current backend. This feature requires Ollama-specific model management capabilities."
    )


@router.post(
    "/api/pull",
    summary="Pull Model (Not Supported)",
    description="Pull a model from registry - not supported with current backend",
    responses={
        501: {"description": "Not implemented with current backend"},
    }
)
async def pull_model(request: OllamaPullRequest) -> Dict[str, str]:
    """
    Pull model endpoint stub.
    
    This endpoint is not supported with the current OpenAI-compatible backend.
    """
    logger.warning(f"Pull model request not supported: {request.model}")
    raise HTTPException(
        status_code=501,
        detail="Model pulling is not supported with the current backend. This feature requires Ollama-specific model registry capabilities."
    )


@router.post(
    "/api/push",
    summary="Push Model (Not Supported)",
    description="Push a model to registry - not supported with current backend",
    responses={
        501: {"description": "Not implemented with current backend"},
    }
)
async def push_model(request: OllamaPushRequest) -> Dict[str, str]:
    """
    Push model endpoint stub.
    
    This endpoint is not supported with the current OpenAI-compatible backend.
    """
    logger.warning(f"Push model request not supported: {request.model}")
    raise HTTPException(
        status_code=501,
        detail="Model pushing is not supported with the current backend. This feature requires Ollama-specific model registry capabilities."
    )


@router.post(
    "/api/create",
    summary="Create Model (Not Supported)",
    description="Create a model from Modelfile - not supported with current backend",
    responses={
        501: {"description": "Not implemented with current backend"},
    }
)
async def create_model(request: OllamaCreateRequest) -> Dict[str, str]:
    """
    Create model endpoint stub.
    
    This endpoint is not supported with the current OpenAI-compatible backend.
    """
    logger.warning(f"Create model request not supported: {request.model}")
    raise HTTPException(
        status_code=501,
        detail="Model creation from Modelfile is not supported with the current backend. This feature requires Ollama-specific model building capabilities."
    )


# Blob management stub endpoints
@router.head(
    "/api/blobs/{digest}",
    summary="Check Blob Exists (Not Supported)",
    description="Check if a blob exists - not supported with current backend",
    responses={
        501: {"description": "Not implemented with current backend"},
    }
)
async def check_blob_exists(digest: str) -> Dict[str, str]:
    """
    Check blob exists endpoint stub.
    
    This endpoint is not supported with the current OpenAI-compatible backend.
    """
    logger.warning(f"Check blob exists request not supported: {digest}")
    raise HTTPException(
        status_code=501,
        detail="Blob management is not supported with the current backend. This feature requires Ollama-specific blob storage capabilities."
    )


@router.post(
    "/api/blobs/{digest}",
    summary="Create Blob (Not Supported)",
    description="Create a blob - not supported with current backend",
    responses={
        501: {"description": "Not implemented with current backend"},
    }
)
async def create_blob(digest: str) -> Dict[str, str]:
    """
    Create blob endpoint stub.
    
    This endpoint is not supported with the current OpenAI-compatible backend.
    """
    logger.warning(f"Create blob request not supported: {digest}")
    raise HTTPException(
        status_code=501,
        detail="Blob management is not supported with the current backend. This feature requires Ollama-specific blob storage capabilities."
    )
