"""
Model filtering service for text-to-image model identification.

This module provides filtering capabilities to identify text-to-image models
from a list of available models using configurable patterns and keywords.
"""

import logging
import re

from app.api.schemas.medusa_schemas import MedusaModel
from app.core.config import Settings

logger = logging.getLogger(__name__)


class ModelFilterService:
    """
    Service for filtering models based on configurable patterns and keywords.

    Provides functionality to identify text-to-image models from a list of
    available models using regex patterns and keyword matching.
    """

    def __init__(self, settings: Settings):
        """
        Initialize the model filter service.

        Args:
            settings: Application settings containing filter configuration
        """
        self.settings = settings
        self.tti_patterns = settings.tti_model_patterns
        self.tti_keywords = settings.tti_model_keywords

        logger.info(
            f"Model filter service initialized with {len(self.tti_patterns)} patterns "
            f"and {len(self.tti_keywords)} keywords"
        )

    def filter_tti_models(self, models: list[MedusaModel]) -> list[MedusaModel]:
        """
        Filter models to return only text-to-image models.

        Applies both pattern matching and keyword filtering. If both are configured,
        returns models that match either criteria. If neither is configured,
        returns all models.

        Args:
            models: List of models to filter

        Returns:
            List[MedusaModel]: Filtered list of text-to-image models
        """
        if not models:
            logger.debug("No models provided for filtering")
            return []

        # If no filters configured, return all models
        if not self.tti_patterns and not self.tti_keywords:
            logger.debug("No TTI filters configured, returning all models")
            return models

        try:
            filtered_models = []

            for model in models:
                model_id = model.id.lower()  # Case-insensitive matching

                # Check if model matches any pattern
                pattern_match = self._matches_patterns(model_id, self.tti_patterns)

                # Check if model contains any keyword
                keyword_match = self._contains_keywords(model_id, self.tti_keywords)

                # Include model if it matches either patterns or keywords
                if pattern_match or keyword_match:
                    filtered_models.append(model)
                    logger.debug(f"Model '{model.id}' matched TTI criteria")

            logger.info(
                f"Filtered {len(models)} models to {len(filtered_models)} TTI models"
            )
            return filtered_models

        except Exception as e:
            logger.error(f"Error during TTI model filtering: {e}")
            logger.warning("Falling back to returning all models")
            return models

    def _matches_patterns(self, model_id: str, patterns: list[str]) -> bool:
        """
        Check if model ID matches any of the provided regex patterns.

        Args:
            model_id: Model ID to check (should be lowercase)
            patterns: List of regex patterns to match against

        Returns:
            bool: True if model matches any pattern, False otherwise
        """
        if not patterns:
            return False

        try:
            for pattern in patterns:
                # Use case-insensitive regex matching
                if re.search(pattern, model_id, re.IGNORECASE):
                    logger.debug(f"Model '{model_id}' matched pattern '{pattern}'")
                    return True
            return False

        except re.error as e:
            logger.error(f"Invalid regex pattern in TTI_MODEL_PATTERNS: {e}")
            # Re-raise the exception to trigger fallback behavior
            raise

    def _contains_keywords(self, model_id: str, keywords: list[str]) -> bool:
        """
        Check if model ID contains any of the provided keywords.

        Args:
            model_id: Model ID to check (should be lowercase)
            keywords: List of keywords to search for (should be lowercase)

        Returns:
            bool: True if model contains any keyword, False otherwise
        """
        if not keywords:
            return False

        for keyword in keywords:
            if keyword in model_id:
                logger.debug(f"Model '{model_id}' contains keyword '{keyword}'")
                return True
        return False


def get_model_filter_service(settings: Settings) -> ModelFilterService:
    """
    Dependency function to get the model filter service.

    Args:
        settings: Application settings

    Returns:
        ModelFilterService: Configured model filter service
    """
    return ModelFilterService(settings)
