# Ollama-to-OpenAI API Bridge Configuration
# Copy this file to .env and update the values for your environment

# Backend API Configuration (REQUIRED)
MEDUSA_BACKEND_URL=https://api.medusaxd.com

# API Configuration
API_TIMEOUT=30
MAX_CONCURRENT_REQUESTS=100

# Model Mapping Configuration (JSON format) - UNUSED in transparent passthrough mode
# NOTE: This bridge now operates in transparent passthrough mode, exposing all backend
# models with their original names without any mapping or transformation.
# The MODEL_MAPPINGS configuration is kept for backward compatibility but is not used.
# All backend models from the OpenAI-compatible API are exposed directly to Ollama clients.
MODEL_MAPPINGS={"llama3": "gpt-4o-mini", "codellama": "gpt-4", "mistral": "gpt-3.5-turbo"}

# Text-to-Image Model Filtering Configuration (JSON format)
# Regex patterns to identify text-to-image models
TTI_MODEL_PATTERNS=[".*-xl$", ".*-turbo$", "stable-diffusion.*", "dalle.*"]

# Keywords to identify text-to-image models (case-insensitive)
TTI_MODEL_KEYWORDS=["diffusion", "dalle", "midjourney", "xl", "turbo", "image", "art"]

# Logging Configuration
LOG_LEVEL=INFO

# Application Configuration
APP_NAME=Ollama-to-OpenAI Bridge
DEBUG=false

# Development Configuration (for local development only)
# MEDUSA_BACKEND_URL=http://localhost:8080
# DEBUG=true
# LOG_LEVEL=DEBUG
