"""API schemas package."""

from .ollama_schemas import (
    OllamaMessage,
    OllamaChatRequest,
    OllamaResponseMessage,
    OllamaChatResponse,
    OllamaStreamResponse,
    OllamaErrorResponse,
    OllamaModel,
    OllamaTagsResponse,
    OllamaShowRequest,
    OllamaModelDetails,
    OllamaShowResponse,
)

from .medusa_schemas import (
    MedusaMessage,
    MedusaChatRequest,
    MedusaUsage,
    MedusaChoice,
    MedusaChatResponse,
    MedusaStreamChoice,
    MedusaStreamResponse,
    MedusaErrorResponse,
    MedusaErrorDetail,
    MedusaModel,
    MedusaModelListResponse,
    MedusaImageGenerationRequest,
    MedusaImageData,
    MedusaImageGenerationResponse,
)

from .search_schemas import (
    SearchEngine,
    SearchRequest,
    SearchResult,
    SearchResponse,
    SearchErrorResponse,
)

__all__ = [
    # Ollama schemas
    "OllamaMessage",
    "OllamaChatRequest",
    "OllamaResponseMessage",
    "OllamaChatResponse",
    "OllamaStreamResponse",
    "OllamaErrorResponse",
    "OllamaModel",
    "OllamaTagsResponse",
    "OllamaShowRequest",
    "OllamaModelDetails",
    "OllamaShowResponse",
    # Medusa schemas
    "MedusaMessage",
    "MedusaChatRequest",
    "MedusaUsage",
    "MedusaChoice",
    "MedusaChatResponse",
    "MedusaStreamChoice",
    "MedusaStreamResponse",
    "MedusaErrorResponse",
    "MedusaErrorDetail",
    "MedusaModel",
    "MedusaModelListResponse",
    "MedusaImageGenerationRequest",
    "MedusaImageData",
    "MedusaImageGenerationResponse",
    # Search schemas
    "SearchEngine",
    "SearchRequest",
    "SearchResult",
    "SearchResponse",
    "SearchErrorResponse",
]
