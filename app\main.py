"""
FastAPI application entry point for Ollama-to-OpenAI API Bridge.

This module initializes the FastAPI application, configures middleware,
includes routers, and sets up the application lifecycle.
"""

import logging
from collections.abc import AsyncGenerator
from contextlib import asynccontextmanager

from fastapi import <PERSON><PERSON><PERSON>, Request, Response
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from fastapi.exceptions import RequestValidationError
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import JSONResponse
from pydantic import ValidationError

from app.api.routers import (
    chat_router,
    health_router,
    models_router,
    v1_router,
    generate_router,
    embeddings_router,
    additional_router
)
from app.core.config import get_settings
from app.core.logging import setup_logging


class URLNormalizationMiddleware(BaseHTTPMiddleware):
    """
    Middleware to normalize URLs by removing duplicate slashes.

    This middleware handles cases where clients send requests with double slashes
    (e.g., //api/tags) by redirecting them to the normalized single slash version
    (e.g., /api/tags).
    """

    async def dispatch(self, request: Request, call_next) -> Response:
        """
        Process the request and normalize the URL path.

        Args:
            request: The incoming HTTP request
            call_next: The next middleware or route handler

        Returns:
            Response: The HTTP response
        """
        # Get the original path
        original_path = request.url.path

        # Normalize the path by removing duplicate slashes
        normalized_path = original_path
        while "//" in normalized_path:
            normalized_path = normalized_path.replace("//", "/")

        # If the path was changed, redirect to the normalized version
        if normalized_path != original_path:
            # Construct the new URL with normalized path
            new_url = request.url.replace(path=normalized_path)

            # Return a 301 permanent redirect to the normalized URL
            return Response(
                status_code=301,
                headers={"Location": str(new_url)}
            )

        # Path is already normalized, continue with the request
        return await call_next(request)


@asynccontextmanager
async def lifespan(app: FastAPI) -> AsyncGenerator[None, None]:
    """
    Application lifespan manager.

    Handles startup and shutdown events for the FastAPI application.
    """
    # Startup
    settings = get_settings()
    setup_logging(settings.log_level)

    logger = logging.getLogger(__name__)
    logger.info("Starting Ollama-to-OpenAI API Bridge")
    logger.info(f"Backend URL: {settings.medusa_backend_url}")

    yield

    # Shutdown
    logger.info("Shutting down Ollama-to-OpenAI API Bridge")


async def validation_exception_handler(request: Request, exc: RequestValidationError):
    """
    Custom handler for validation errors to provide better debugging information.
    """
    logger = logging.getLogger(__name__)

    # Log the raw request details
    try:
        body = await request.body()
        logger.error(f"Validation error for {request.method} {request.url}")
        logger.error(f"Request headers: {dict(request.headers)}")
        logger.error(f"Request body: {body.decode('utf-8') if body else 'Empty'}")
        logger.error(f"Validation errors: {exc.errors()}")
    except Exception as e:
        logger.error(f"Error logging validation details: {e}")

    return JSONResponse(
        status_code=422,
        content={
            "detail": exc.errors(),
            "body": body.decode('utf-8') if body else None
        }
    )


def create_app() -> FastAPI:
    """
    Create and configure the FastAPI application.

    Returns:
        FastAPI: Configured FastAPI application instance
    """
    app = FastAPI(
        title="Ollama-to-OpenAI API Bridge",
        description="Translation middleware for seamless switching between Ollama and OpenAI-compatible APIs",
        version="0.1.0",
        docs_url="/docs",
        redoc_url="/redoc",
        lifespan=lifespan,
    )

    # Add custom exception handlers
    app.add_exception_handler(RequestValidationError, validation_exception_handler)

    # Add URL normalization middleware (should be first)
    app.add_middleware(URLNormalizationMiddleware)

    # Add GZip compression middleware for large responses (like /api/tags with 588 models)
    app.add_middleware(GZipMiddleware, minimum_size=1000)

    # Add CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],  # Configure appropriately for production
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

    # Include routers
    app.include_router(health_router, tags=["Health"])
    app.include_router(chat_router, tags=["Chat"])
    app.include_router(models_router, tags=["Models"])
    app.include_router(generate_router, tags=["Generate"])
    app.include_router(embeddings_router, tags=["Embeddings"])
    app.include_router(additional_router, tags=["Additional"])
    app.include_router(v1_router, tags=["V1"])

    return app


# Create the application instance
app = create_app()


if __name__ == "__main__":
    import uvicorn

    settings = get_settings()
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level=settings.log_level.lower(),
    )
