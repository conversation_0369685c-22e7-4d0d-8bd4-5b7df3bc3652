"""
Search API schemas for web search functionality.

This module contains Pydantic models for web search requests and responses,
supporting multiple search engines and configurable parameters.
"""

from enum import Enum
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field


class SearchEngine(str, Enum):
    """Supported search engines."""
    GOOGLE = "google"
    BING = "bing"
    DUCKDUCKGO = "duckduckgo"


class SearchRequest(BaseModel):
    """
    Represents a web search request.
    
    This model defines the parameters for performing a web search
    with configurable options for search engine, results count,
    language, region, and safety settings.
    """
    
    query: str = Field(
        ...,
        description="Search query string",
        min_length=1,
        max_length=1000
    )
    
    engine: Optional[SearchEngine] = Field(
        SearchEngine.GOOGLE,
        description="Search engine to use for the search"
    )
    
    num_results: Optional[int] = Field(
        10,
        description="Number of search results to return",
        ge=1,
        le=100
    )
    
    language: Optional[str] = Field(
        "en",
        description="Language code for search results (e.g., 'en', 'es', 'fr')",
        min_length=2,
        max_length=5
    )
    
    region: Optional[str] = Field(
        None,
        description="Region/country code for localized results (e.g., 'US', 'UK', 'CA')",
        min_length=2,
        max_length=5
    )
    
    safe_search: Optional[bool] = Field(
        True,
        description="Enable safe search filtering to exclude explicit content"
    )


class SearchResult(BaseModel):
    """
    Represents a single search result.
    
    Contains the essential information for each search result including
    title, URL, snippet, and ranking position.
    """
    
    title: str = Field(
        ...,
        description="Title of the search result"
    )
    
    url: str = Field(
        ...,
        description="URL of the search result"
    )
    
    snippet: str = Field(
        ...,
        description="Brief description or excerpt from the search result"
    )
    
    rank: int = Field(
        ...,
        description="Ranking position of this result (1-based)",
        ge=1
    )


class SearchResponse(BaseModel):
    """
    Represents the response from a web search request.
    
    Contains the search results along with metadata about the search
    including the original query, search engine used, and performance metrics.
    """
    
    query: str = Field(
        ...,
        description="The original search query that was executed"
    )
    
    engine: str = Field(
        ...,
        description="The search engine that was used"
    )
    
    results: List[SearchResult] = Field(
        ...,
        description="List of search results"
    )
    
    total_results: Optional[int] = Field(
        None,
        description="Total number of results available (may be more than returned)",
        ge=0
    )
    
    search_time: Optional[float] = Field(
        None,
        description="Time taken to perform the search in seconds",
        ge=0.0
    )
    
    language: Optional[str] = Field(
        None,
        description="Language code used for the search"
    )
    
    region: Optional[str] = Field(
        None,
        description="Region code used for the search"
    )


class SearchErrorResponse(BaseModel):
    """
    Represents an error response from the search API.
    
    Used when search requests fail due to various reasons such as
    invalid parameters, backend errors, or service unavailability.
    """
    
    error: str = Field(
        ...,
        description="Error type or code"
    )
    
    message: str = Field(
        ...,
        description="Human-readable error message"
    )
    
    query: Optional[str] = Field(
        None,
        description="The query that caused the error (if applicable)"
    )
    
    details: Optional[Dict[str, Any]] = Field(
        None,
        description="Additional error details"
    )
