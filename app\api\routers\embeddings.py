"""
Embeddings endpoint router for Ollama-compatible API.

This module provides the /api/embed and /api/embeddings endpoints that accept 
Ollama-formatted embeddings requests and translate them to/from the backend API format.
"""

import logging
from typing import Dict, Any

from fastapi import APIRouter, Depends, HTTPException, status

from app.api.schemas.ollama_schemas import (
    OllamaEmbedRequest, 
    OllamaEmbedResponse,
    OllamaEmbeddingsRequest,
    OllamaEmbeddingsResponse,
)
from app.core.config import Settings, get_settings
from app.services.translation_service import TranslationService
from app.services.backend_client_service import (
    BackendClientService,
    BackendClientError,
    BackendTimeoutError,
    BackendConnectionError,
    BackendHTTPError,
    get_backend_client,
)

logger = logging.getLogger(__name__)

# Create router
router = APIRouter()


def get_translation_service(settings: Settings = Depends(get_settings)) -> TranslationService:
    """
    Dependency to get the translation service.

    Args:
        settings: Application settings

    Returns:
        TranslationService: Configured translation service
    """
    return TranslationService(settings)


async def get_backend_client_dependency(settings: Settings = Depends(get_settings)) -> BackendClientService:
    """
    Dependency to get the backend client service.

    Args:
        settings: Application settings

    Returns:
        BackendClientService: Configured backend client
    """
    return get_backend_client(settings)


@router.post(
    "/api/embed",
    response_model=OllamaEmbedResponse,
    summary="Generate Embeddings",
    description="Generate embeddings for input text in Ollama format",
    responses={
        200: {"description": "Successful embeddings generation"},
        400: {"description": "Invalid request format or parameters"},
        502: {"description": "Backend service error"},
        504: {"description": "Backend service timeout"},
    }
)
async def generate_embeddings(
    request: OllamaEmbedRequest,
    translation_service: TranslationService = Depends(get_translation_service),
    backend_client: BackendClientService = Depends(get_backend_client_dependency),
) -> OllamaEmbedResponse:
    """
    Handle embeddings requests in Ollama format.
    
    This endpoint accepts Ollama-formatted embeddings requests, translates them to
    OpenAI embeddings format for the backend, and translates the response back to Ollama format.
    
    Args:
        request: The Ollama embeddings request
        translation_service: Service for format translation
        backend_client: Client for backend API communication
        
    Returns:
        OllamaEmbedResponse: The embeddings response in Ollama format
        
    Raises:
        HTTPException: For various error conditions (400, 502, 504)
    """
    logger.info(f"Embeddings request received for model: {request.model}")
    logger.debug(f"Request details: input_type={type(request.input)}")
    
    try:
        # Validate request
        if not request.model:
            logger.warning("Embeddings request missing model")
            raise HTTPException(
                status_code=400,
                detail="Model name is required"
            )
        
        if not request.input:
            logger.warning("Embeddings request missing input")
            raise HTTPException(
                status_code=400,
                detail="Input is required"
            )
        
        # Translate Ollama request to Medusa format
        logger.debug("Translating Ollama embeddings request to backend format")
        medusa_request = translation_service.translate_embed_request(request)
        
        # Send request to backend
        logger.debug("Sending embeddings request to backend")
        medusa_response = await backend_client.generate_embeddings(medusa_request)
        
        # Translate response back to Ollama format
        logger.debug("Translating backend response to Ollama format")
        ollama_response = translation_service.translate_embed_response(medusa_response, request.model)
        
        logger.info(f"Embeddings generation successful for model: {request.model}")
        return ollama_response
        
    except HTTPException:
        # Re-raise HTTP exceptions (like 400, 404)
        raise
        
    except BackendTimeoutError as e:
        logger.error(f"Backend timeout during embeddings generation: {e}")
        raise HTTPException(
            status_code=504,
            detail="Backend service timeout while generating embeddings"
        ) from e
        
    except BackendConnectionError as e:
        logger.error(f"Backend connection error during embeddings generation: {e}")
        raise HTTPException(
            status_code=502,
            detail="Backend service connection error"
        ) from e
        
    except BackendHTTPError as e:
        logger.error(f"Backend HTTP error during embeddings generation: {e}")
        
        # Map backend errors to appropriate HTTP status codes
        if e.status_code == 404:
            raise HTTPException(
                status_code=404,
                detail=f"Model '{request.model}' not found"
            ) from e
        elif e.status_code == 400:
            raise HTTPException(
                status_code=400,
                detail="Invalid request parameters"
            ) from e
        else:
            raise HTTPException(
                status_code=502,
                detail="Backend service error"
            ) from e
            
    except BackendClientError as e:
        logger.error(f"Backend client error during embeddings generation: {e}")
        raise HTTPException(
            status_code=502,
            detail="Backend service error"
        ) from e
        
    except Exception as e:
        logger.error(f"Unexpected error during embeddings generation: {e}")
        raise HTTPException(
            status_code=500,
            detail="Internal server error"
        ) from e


@router.post(
    "/api/embeddings",
    response_model=OllamaEmbeddingsResponse,
    summary="Generate Embedding (Legacy)",
    description="Generate embedding for input text in legacy Ollama format",
    responses={
        200: {"description": "Successful embedding generation"},
        400: {"description": "Invalid request format or parameters"},
        502: {"description": "Backend service error"},
        504: {"description": "Backend service timeout"},
    }
)
async def generate_embedding_legacy(
    request: OllamaEmbeddingsRequest,
    translation_service: TranslationService = Depends(get_translation_service),
    backend_client: BackendClientService = Depends(get_backend_client_dependency),
) -> OllamaEmbeddingsResponse:
    """
    Handle legacy embeddings requests in Ollama format.
    
    This endpoint accepts Ollama-formatted legacy embeddings requests, translates them to
    OpenAI embeddings format for the backend, and translates the response back to legacy Ollama format.
    
    Args:
        request: The Ollama legacy embeddings request
        translation_service: Service for format translation
        backend_client: Client for backend API communication
        
    Returns:
        OllamaEmbeddingsResponse: The legacy embeddings response in Ollama format
        
    Raises:
        HTTPException: For various error conditions (400, 502, 504)
    """
    logger.info(f"Legacy embeddings request received for model: {request.model}")
    logger.debug(f"Request details: prompt_length={len(request.prompt)}")
    
    try:
        # Validate request
        if not request.model:
            logger.warning("Legacy embeddings request missing model")
            raise HTTPException(
                status_code=400,
                detail="Model name is required"
            )
        
        if not request.prompt:
            logger.warning("Legacy embeddings request missing prompt")
            raise HTTPException(
                status_code=400,
                detail="Prompt is required"
            )
        
        # Translate Ollama request to Medusa format
        logger.debug("Translating Ollama legacy embeddings request to backend format")
        medusa_request = translation_service.translate_embeddings_request(request)
        
        # Send request to backend
        logger.debug("Sending embeddings request to backend")
        medusa_response = await backend_client.generate_embeddings(medusa_request)
        
        # Translate response back to Ollama legacy format
        logger.debug("Translating backend response to Ollama legacy format")
        ollama_response = translation_service.translate_embeddings_response(medusa_response, request.model)
        
        logger.info(f"Legacy embeddings generation successful for model: {request.model}")
        return ollama_response
        
    except HTTPException:
        # Re-raise HTTP exceptions (like 400, 404)
        raise
        
    except BackendTimeoutError as e:
        logger.error(f"Backend timeout during legacy embeddings generation: {e}")
        raise HTTPException(
            status_code=504,
            detail="Backend service timeout while generating embeddings"
        ) from e
        
    except BackendConnectionError as e:
        logger.error(f"Backend connection error during legacy embeddings generation: {e}")
        raise HTTPException(
            status_code=502,
            detail="Backend service connection error"
        ) from e
        
    except BackendHTTPError as e:
        logger.error(f"Backend HTTP error during legacy embeddings generation: {e}")
        
        # Map backend errors to appropriate HTTP status codes
        if e.status_code == 404:
            raise HTTPException(
                status_code=404,
                detail=f"Model '{request.model}' not found"
            ) from e
        elif e.status_code == 400:
            raise HTTPException(
                status_code=400,
                detail="Invalid request parameters"
            ) from e
        else:
            raise HTTPException(
                status_code=502,
                detail="Backend service error"
            ) from e
            
    except BackendClientError as e:
        logger.error(f"Backend client error during legacy embeddings generation: {e}")
        raise HTTPException(
            status_code=502,
            detail="Backend service error"
        ) from e
        
    except Exception as e:
        logger.error(f"Unexpected error during legacy embeddings generation: {e}")
        raise HTTPException(
            status_code=500,
            detail="Internal server error"
        ) from e
