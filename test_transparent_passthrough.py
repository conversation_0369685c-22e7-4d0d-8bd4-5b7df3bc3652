#!/usr/bin/env python3
"""
Test script to verify transparent passthrough of model names.

This script tests that the Ollama-to-OpenAI bridge correctly exposes all backend
models with their original names without any filtering or transformation.
"""

import asyncio
import json
import logging
from typing import List

from app.api.schemas.medusa_schemas import MedusaModel, MedusaModelListResponse
from app.api.schemas.ollama_schemas import OllamaTagsResponse
from app.core.config import get_settings
from app.services.translation_service import TranslationService

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def create_test_backend_models() -> List[MedusaModel]:
    """Create a list of test backend models with various names."""
    return [
        MedusaModel(
            id="gpt-4o-mini",
            object="model",
            created=1234567890,
            owned_by="openai"
        ),
        MedusaModel(
            id="gpt-4-turbo",
            object="model", 
            created=1234567891,
            owned_by="openai"
        ),
        MedusaModel(
            id="claude-3-sonnet",
            object="model",
            created=1234567892,
            owned_by="anthropic"
        ),
        MedusaModel(
            id="llama-3.1-70b-instruct",
            object="model",
            created=1234567893,
            owned_by="meta"
        ),
        MedusaModel(
            id="mistral-7b-instruct-v0.3",
            object="model",
            created=1234567894,
            owned_by="mistralai"
        ),
        MedusaModel(
            id="qwen2.5-72b-instruct",
            object="model",
            created=1234567895,
            owned_by="alibaba"
        )
    ]


def test_transparent_passthrough():
    """Test that model names are passed through transparently."""
    logger.info("Testing transparent passthrough of model names...")
    
    # Create test data
    backend_models = create_test_backend_models()
    backend_response = MedusaModelListResponse(
        object="list",
        data=backend_models
    )
    
    # Initialize translation service
    settings = get_settings()
    translation_service = TranslationService(settings)
    
    # Translate to Ollama format
    ollama_response = translation_service.translate_model_list(backend_response)
    
    # Verify all models are present
    assert len(ollama_response.models) == len(backend_models), \
        f"Expected {len(backend_models)} models, got {len(ollama_response.models)}"
    
    # Verify model names are preserved exactly
    backend_model_names = {model.id for model in backend_models}
    ollama_model_names = {model.name for model in ollama_response.models}
    
    assert backend_model_names == ollama_model_names, \
        f"Model names don't match!\nBackend: {sorted(backend_model_names)}\nOllama: {sorted(ollama_model_names)}"
    
    # Verify each model individually
    for backend_model in backend_models:
        ollama_model = next(
            (m for m in ollama_response.models if m.name == backend_model.id),
            None
        )
        assert ollama_model is not None, f"Backend model {backend_model.id} not found in Ollama response"
        assert ollama_model.model == backend_model.id, f"Model field mismatch for {backend_model.id}"
        
        logger.info(f"✓ Model '{backend_model.id}' correctly passed through as '{ollama_model.name}'")
    
    logger.info("✅ All tests passed! Transparent passthrough is working correctly.")
    
    # Print summary
    print("\n" + "="*60)
    print("TRANSPARENT PASSTHROUGH TEST RESULTS")
    print("="*60)
    print(f"Backend models tested: {len(backend_models)}")
    print(f"Ollama models returned: {len(ollama_response.models)}")
    print("\nModel name mapping verification:")
    for backend_model in backend_models:
        print(f"  {backend_model.id} → {backend_model.id} ✓")
    print("\n✅ All backend models exposed with original names!")
    print("✅ No filtering, mapping, or transformation applied!")
    print("="*60)


if __name__ == "__main__":
    test_transparent_passthrough()
