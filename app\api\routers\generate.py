"""
Generate endpoint router for Ollama-compatible API.

This module provides the /api/generate endpoint that accepts Ollama-formatted
generate requests and translates them to/from the backend API format.
"""

import logging
from typing import Dict, Any, AsyncGenerator
import json

from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.responses import JSONResponse, StreamingResponse

from app.api.schemas.ollama_schemas import OllamaGenerateRequest, OllamaGenerateResponse
from app.core.config import Settings, get_settings
from app.services.translation_service import TranslationService
from app.services.backend_client_service import (
    BackendClientService,
    BackendClientError,
    BackendTimeoutError,
    BackendConnectionError,
    BackendHTTPError,
    get_backend_client,
)

logger = logging.getLogger(__name__)

# Create router
router = APIRouter()


def get_translation_service(settings: Settings = Depends(get_settings)) -> TranslationService:
    """
    Dependency to get the translation service.

    Args:
        settings: Application settings

    Returns:
        TranslationService: Configured translation service
    """
    return TranslationService(settings)


async def get_backend_client_dependency(settings: Settings = Depends(get_settings)) -> BackendClientService:
    """
    Dependency to get the backend client service.

    Args:
        settings: Application settings

    Returns:
        BackendClientService: Configured backend client
    """
    return get_backend_client(settings)


@router.post(
    "/api/generate",
    response_model=OllamaGenerateResponse,
    summary="Generate Completion",
    description="Generate a completion for a given prompt in Ollama format",
    responses={
        200: {"description": "Successful completion generation"},
        400: {"description": "Invalid request format or parameters"},
        502: {"description": "Backend service error"},
        504: {"description": "Backend service timeout"},
    }
)
async def generate_completion(
    request: OllamaGenerateRequest,
    translation_service: TranslationService = Depends(get_translation_service),
    backend_client: BackendClientService = Depends(get_backend_client_dependency),
) -> OllamaGenerateResponse:
    """
    Handle generate completion requests in Ollama format.
    
    This endpoint accepts Ollama-formatted generate requests, translates them to
    OpenAI completion format for the backend, and translates the response back to Ollama format.
    
    Args:
        request: The Ollama generate completion request
        translation_service: Service for format translation
        backend_client: Client for backend API communication
        
    Returns:
        OllamaGenerateResponse: The generate completion response in Ollama format
        
    Raises:
        HTTPException: For various error conditions (400, 502, 504)
    """
    logger.info(f"Generate completion request received for model: {request.model}")
    logger.debug(f"Request details: prompt_length={len(request.prompt)}, stream={request.stream}")
    
    try:
        # Validate request
        if not request.model:
            logger.warning("Generate request missing model")
            raise HTTPException(
                status_code=400,
                detail="Model name is required"
            )
        
        if not request.prompt:
            logger.warning("Generate request missing prompt")
            raise HTTPException(
                status_code=400,
                detail="Prompt is required"
            )
        
        # Check for streaming request
        if request.stream:
            logger.debug("Streaming generate completion not yet implemented")
            raise HTTPException(
                status_code=501,
                detail="Streaming generate completions not yet implemented"
            )
        
        # Translate Ollama request to Medusa format
        logger.debug("Translating Ollama generate request to backend format")
        medusa_request = translation_service.translate_generate_request(request)
        
        # Send request to backend
        logger.debug("Sending completion request to backend")
        medusa_response = await backend_client.generate_completion(medusa_request)
        
        # Translate response back to Ollama format
        logger.debug("Translating backend response to Ollama format")
        ollama_response = translation_service.translate_generate_response(medusa_response, request.model)
        
        logger.info(f"Generate completion successful for model: {request.model}")
        return ollama_response
        
    except HTTPException:
        # Re-raise HTTP exceptions (like 400, 404)
        raise
        
    except BackendTimeoutError as e:
        logger.error(f"Backend timeout during generate completion: {e}")
        raise HTTPException(
            status_code=504,
            detail="Backend service timeout while generating completion"
        ) from e
        
    except BackendConnectionError as e:
        logger.error(f"Backend connection error during generate completion: {e}")
        raise HTTPException(
            status_code=502,
            detail="Backend service connection error"
        ) from e
        
    except BackendHTTPError as e:
        logger.error(f"Backend HTTP error during generate completion: {e}")
        
        # Map backend errors to appropriate HTTP status codes
        if e.status_code == 404:
            raise HTTPException(
                status_code=404,
                detail=f"Model '{request.model}' not found"
            ) from e
        elif e.status_code == 400:
            raise HTTPException(
                status_code=400,
                detail="Invalid request parameters"
            ) from e
        else:
            raise HTTPException(
                status_code=502,
                detail="Backend service error"
            ) from e
            
    except BackendClientError as e:
        logger.error(f"Backend client error during generate completion: {e}")
        raise HTTPException(
            status_code=502,
            detail="Backend service error"
        ) from e
        
    except Exception as e:
        logger.error(f"Unexpected error during generate completion: {e}")
        raise HTTPException(
            status_code=500,
            detail="Internal server error"
        ) from e


@router.get(
    "/api/generate/health",
    summary="Generate Service Health Check",
    description="Check the health of the generate completion service",
    responses={
        200: {"description": "Service is healthy"},
        502: {"description": "Backend service error"},
        504: {"description": "Backend service timeout"},
    }
)
async def generate_health_check(
    backend_client: BackendClientService = Depends(get_backend_client_dependency),
) -> Dict[str, Any]:
    """
    Health check endpoint for the generate service.
    
    Verifies that the backend service is reachable and responsive.
    
    Args:
        backend_client: Client for backend API communication
        
    Returns:
        Dict[str, Any]: Health status information
        
    Raises:
        HTTPException: If backend is not healthy
    """
    logger.debug("Generate service health check requested")
    
    try:
        # Check backend connectivity
        backend_health = await backend_client.health_check()
        
        return {
            "status": "healthy",
            "service": "generate",
            "backend_status": "connected",
            "backend_health": backend_health
        }
        
    except BackendTimeoutError as e:
        logger.error(f"Backend timeout during generate health check: {e}")
        raise HTTPException(
            status_code=504,
            detail="Backend service timeout"
        ) from e
        
    except (BackendConnectionError, BackendHTTPError, BackendClientError) as e:
        logger.error(f"Backend error during generate health check: {e}")
        raise HTTPException(
            status_code=502,
            detail="Backend service error"
        ) from e
        
    except Exception as e:
        logger.error(f"Unexpected error during generate health check: {e}")
        raise HTTPException(
            status_code=500,
            detail="Internal server error"
        ) from e
