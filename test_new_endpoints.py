#!/usr/bin/env python3
"""
Test script for new Ollama API endpoints.

This script tests the newly implemented endpoints to ensure they work correctly.
"""

import asyncio
import json
import sys
from typing import Dict, Any

import httpx


async def test_endpoint(client: httpx.AsyncClient, method: str, url: str, data: Dict[str, Any] = None) -> Dict[str, Any]:
    """Test a single endpoint."""
    try:
        if method.upper() == "GET":
            response = await client.get(url)
        elif method.upper() == "POST":
            response = await client.post(url, json=data)
        elif method.upper() == "DELETE":
            response = await client.delete(url, json=data)
        elif method.upper() == "HEAD":
            response = await client.head(url)
        else:
            return {"error": f"Unsupported method: {method}"}
        
        return {
            "status_code": response.status_code,
            "response": response.json() if response.content else {},
            "success": response.status_code < 400
        }
    except Exception as e:
        return {"error": str(e), "success": False}


async def main():
    """Main test function."""
    base_url = "http://localhost:8000"
    
    # Test cases for new endpoints
    test_cases = [
        # Generate endpoint
        {
            "name": "Generate Completion",
            "method": "POST",
            "url": f"{base_url}/api/generate",
            "data": {
                "model": "gpt-3.5-turbo",
                "prompt": "What is the capital of France?",
                "stream": False
            }
        },
        
        # Embeddings endpoint
        {
            "name": "Generate Embeddings",
            "method": "POST", 
            "url": f"{base_url}/api/embed",
            "data": {
                "model": "text-embedding-ada-002",
                "input": "Hello world"
            }
        },
        
        # Legacy embeddings endpoint
        {
            "name": "Generate Legacy Embeddings",
            "method": "POST",
            "url": f"{base_url}/api/embeddings", 
            "data": {
                "model": "text-embedding-ada-002",
                "prompt": "Hello world"
            }
        },
        
        # Running models endpoint
        {
            "name": "List Running Models",
            "method": "GET",
            "url": f"{base_url}/api/ps"
        },
        
        # Model management stubs (should return 501)
        {
            "name": "Copy Model (Should Fail)",
            "method": "POST",
            "url": f"{base_url}/api/copy",
            "data": {
                "source": "model1",
                "destination": "model2"
            },
            "expected_status": 501
        },
        
        {
            "name": "Delete Model (Should Fail)",
            "method": "DELETE",
            "url": f"{base_url}/api/delete",
            "data": {
                "model": "test-model"
            },
            "expected_status": 501
        },
        
        {
            "name": "Pull Model (Should Fail)",
            "method": "POST",
            "url": f"{base_url}/api/pull",
            "data": {
                "model": "test-model"
            },
            "expected_status": 501
        },
        
        {
            "name": "Push Model (Should Fail)",
            "method": "POST",
            "url": f"{base_url}/api/push",
            "data": {
                "model": "test-model"
            },
            "expected_status": 501
        },
        
        {
            "name": "Create Model (Should Fail)",
            "method": "POST",
            "url": f"{base_url}/api/create",
            "data": {
                "model": "test-model",
                "modelfile": "FROM llama3"
            },
            "expected_status": 501
        },
        
        {
            "name": "Check Blob Exists (Should Fail)",
            "method": "HEAD",
            "url": f"{base_url}/api/blobs/sha256:test",
            "expected_status": 501
        },
        
        {
            "name": "Create Blob (Should Fail)",
            "method": "POST",
            "url": f"{base_url}/api/blobs/sha256:test",
            "expected_status": 501
        }
    ]
    
    print("Testing new Ollama API endpoints...")
    print("=" * 50)
    
    async with httpx.AsyncClient(timeout=30.0) as client:
        results = []
        
        for test_case in test_cases:
            print(f"\nTesting: {test_case['name']}")
            print(f"Method: {test_case['method']} {test_case['url']}")
            
            result = await test_endpoint(
                client, 
                test_case['method'], 
                test_case['url'], 
                test_case.get('data')
            )
            
            expected_status = test_case.get('expected_status')
            if expected_status:
                success = result.get('status_code') == expected_status
                print(f"Expected Status: {expected_status}, Got: {result.get('status_code')}")
            else:
                success = result.get('success', False)
                print(f"Status: {result.get('status_code')}")
            
            if 'error' in result:
                print(f"Error: {result['error']}")
                success = False
            elif result.get('response'):
                print(f"Response: {json.dumps(result['response'], indent=2)[:200]}...")
            
            print(f"Result: {'✅ PASS' if success else '❌ FAIL'}")
            
            results.append({
                'name': test_case['name'],
                'success': success,
                'status_code': result.get('status_code'),
                'error': result.get('error')
            })
    
    # Summary
    print("\n" + "=" * 50)
    print("SUMMARY")
    print("=" * 50)
    
    passed = sum(1 for r in results if r['success'])
    total = len(results)
    
    print(f"Tests passed: {passed}/{total}")
    
    if passed < total:
        print("\nFailed tests:")
        for result in results:
            if not result['success']:
                print(f"  - {result['name']}: {result.get('error', f'Status {result.get('status_code')}')}") 
    
    return passed == total


if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\nTest interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"Test failed with error: {e}")
        sys.exit(1)
